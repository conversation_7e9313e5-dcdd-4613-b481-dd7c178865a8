import { VatInfo } from '../../lib/validators';
import Stripe from 'stripe';

/**
 * Service to determine VAT application based on Stripe Tax breakdown results
 */
export class VatCalculatorService {
  /**
   * Bulgarian VAT exemption reasons according to ЗДДС (VAT Act)
   */
  private static readonly VAT_EXEMPTION_REASONS = [
    'чл.113 ал.9 от ЗДДС',
    'чл.86, ал.3 и чл.21 от ЗДДС',
    'чл.82, ал. 2 от ЗДДС',
    'чл.21 от ЗДДС',
    'чл.21 ал.2 от ЗДДС',
  ];

  /**
   * Determines VAT information based on Stripe Tax breakdown
   * @param taxBreakdown - Array of tax breakdown items from Stripe
   * @returns VAT information object
   */
  static calculateVatInfoFromStripeBreakdown(
    taxBreakdown: Stripe.Checkout.Session.TotalDetails.Breakdown.Tax[],
  ): VatInfo {
    // If no tax breakdown or empty array, return 0% with default exemption reason
    if (!taxBreakdown || taxBreakdown.length === 0) {
      return {
        percent: 0,
        reason_without: this.VAT_EXEMPTION_REASONS[0], // Default to чл.113 ал.9 от ЗДДС
      };
    }

    // Find VAT entry in the tax breakdown
    // Look for rate.tax_type === 'vat' in the TaxRate object
    const vatEntry = taxBreakdown.find((tax) => tax.rate.tax_type === 'vat');

    // If VAT found and applied, return the percentage
    if (vatEntry && vatEntry.amount > 0) {
      return {
        percent: vatEntry.rate.percentage,
        reason_without: null,
      };
    }

    // If VAT entry exists but no tax applied, use taxability reason for exemption
    if (vatEntry && vatEntry.amount === 0) {
      return {
        percent: 0,
        reason_without: this.mapTaxabilityReasonToBulgarianVAT(
          vatEntry.taxability_reason,
        ),
      };
    }

    // Check if any tax entry has taxability reason that indicates VAT exemption
    const exemptEntry = taxBreakdown.find(
      (tax) =>
        tax.taxability_reason &&
        [
          'reverse_charge',
          'customer_exempt',
          'not_subject_to_tax',
          'zero_rated',
        ].includes(tax.taxability_reason),
    );

    if (exemptEntry) {
      return {
        percent: 0,
        reason_without: this.mapTaxabilityReasonToBulgarianVAT(
          exemptEntry.taxability_reason,
        ),
      };
    }

    // Default case - no VAT found
    return {
      percent: 0,
      reason_without: this.VAT_EXEMPTION_REASONS[0],
    };
  }

  /**
   * Validates if a VAT exemption reason is valid
   * @param reason - The exemption reason to validate
   * @returns True if valid, false otherwise
   */
  static isValidExemptionReason(reason: string): boolean {
    return this.VAT_EXEMPTION_REASONS.includes(reason);
  }

  /**
   * Gets all available VAT exemption reasons
   * @returns Array of valid exemption reasons
   */
  static getAvailableExemptionReasons(): string[] {
    return [...this.VAT_EXEMPTION_REASONS];
  }

  /**
   * Converts VAT percentage from decimal to percentage number
   * @param decimalPercent - VAT percentage as decimal (e.g., 0.20 for 20%)
   * @returns VAT percentage as number (e.g., 20)
   */
  static formatVatPercent(decimalPercent: number): number {
    return decimalPercent * 100;
  }

  /**
   * Parses VAT percentage from number to decimal
   * @param percentNumber - VAT percentage as number (e.g., 20)
   * @returns VAT percentage as decimal (e.g., 0.20)
   */
  static parseVatPercent(percentNumber: number): number {
    return percentNumber / 100;
  }

  /**
   * Maps Stripe taxability reasons to Bulgarian VAT exemption reasons
   */
  private static mapTaxabilityReasonToBulgarianVAT(
    taxabilityReason: string | null,
  ): string {
    switch (taxabilityReason) {
      case 'reverse_charge':
        return this.VAT_EXEMPTION_REASONS[1]; // чл.86, ал.3 и чл.21 от ЗДДС

      case 'zero_rated':
        return this.VAT_EXEMPTION_REASONS[4]; // чл.21 ал.2 от ЗДДС

      case 'customer_exempt':
      case 'not_subject_to_tax':
        return this.VAT_EXEMPTION_REASONS[3]; // чл.21 от ЗДДС

      case 'not_collecting':
      case 'not_supported':
        return this.VAT_EXEMPTION_REASONS[2]; // чл.82, ал. 2 от ЗДДС

      case 'product_exempt':
      case 'product_exempt_holiday':
        return this.VAT_EXEMPTION_REASONS[0]; // чл.113 ал.9 от ЗДДС

      default:
        return this.VAT_EXEMPTION_REASONS[0]; // чл.113 ал.9 от ЗДДС
    }
  }
}
