<div class="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
  <!-- Header Section -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-4xl font-bold text-white mb-2">Proposals Dashboard</h1>
        <p class="text-slate-400 text-lg">Manage and track all your business proposals</p>
      </div>
      <div class="flex items-center gap-4">
        <div class="bg-slate-800/50 backdrop-blur-sm border border-slate-600 rounded-lg px-4 py-2">
          <span class="text-slate-300 text-sm">Total Proposals: </span>
          <span class="text-white font-bold text-lg">{{ proposals.length }}</span>
        </div>
        <button
          (click)="createNewProposal()"
          class="flex items-center gap-2 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/25">
          <i class="pi pi-plus text-sm"></i>
          <span>New Proposal</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Main Content Card -->
  <div class="bg-slate-800/60 backdrop-blur-sm border border-slate-600 rounded-xl shadow-2xl overflow-hidden">
    <!-- Toolbar -->
    <div class="bg-slate-700/50 border-b border-slate-600 p-6">
      <div class="flex items-center justify-between gap-4">
        <div class="flex items-center gap-4 flex-1">
          <!-- Search -->
          <div class="relative flex-1 max-w-md">
            <i class="pi pi-search absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
            <input
              #searchInput
              (input)="onGlobalFilter(dt, $event)"
              [(ngModel)]="searchValue"
              class="w-full bg-slate-900/50 border border-slate-600 rounded-lg pl-10 pr-4 py-3 text-white placeholder-slate-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-200"
              placeholder="Search proposals..."
              type="text"
            />
          </div>

          <!-- Status Filter -->
          <p-dropdown
            [(ngModel)]="selectedStatus"
            [options]="statusOptions"
            [style]="{ minWidth: '200px' }"
            optionLabel="label"
            optionValue="value"
            placeholder="Filter by Status"
            styleClass="custom-dropdown">
          </p-dropdown>
        </div>

        <!-- Clear Button -->
        <button
          (click)="clear(dt)"
          class="flex items-center gap-2 bg-slate-600 hover:bg-slate-500 text-white px-4 py-2 rounded-lg transition-all duration-200">
          <i class="pi pi-filter-slash text-sm"></i>
          <span>Clear</span>
        </button>
      </div>
    </div>

    <!-- Data Table -->
    <p-table
      #dt
      [(selection)]="selectedProposals"
      [globalFilterFields]="['project_title', 'client.name', 'status', 'currency']"
      [loading]="loading"
      [paginator]="true"
      [rowHover]="true"
      [rowsPerPageOptions]="[5, 10, 20, 50]"
      [rows]="10"
      [styleClass]="'custom-table'"
      [tableStyle]="{ 'min-width': '60rem' }"
      [value]="proposals"
      dataKey="id"
      selectionMode="multiple">

      <!-- Table Header -->
      <ng-template pTemplate="header">
        <tr class="bg-slate-700/30">
          <th style="width: 3rem">
            <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
          </th>
          <th class="text-slate-200 font-semibold" pSortableColumn="project_title">
            <div class="flex items-center gap-2">
              <span>Project Title</span>
              <p-sortIcon field="project_title"></p-sortIcon>
            </div>
          </th>
          <th class="text-slate-200 font-semibold" pSortableColumn="client.name">
            <div class="flex items-center gap-2">
              <span>Client</span>
              <p-sortIcon field="client.name"></p-sortIcon>
            </div>
          </th>
          <th class="text-slate-200 font-semibold" pSortableColumn="price">
            <div class="flex items-center gap-2">
              <span>Amount</span>
              <p-sortIcon field="price"></p-sortIcon>
            </div>
          </th>
          <th class="text-slate-200 font-semibold" pSortableColumn="status">
            <div class="flex items-center gap-2">
              <span>Status</span>
              <p-sortIcon field="status"></p-sortIcon>
            </div>
          </th>
          <th class="text-slate-200 font-semibold" pSortableColumn="created_at">
            <div class="flex items-center gap-2">
              <span>Created</span>
              <p-sortIcon field="created_at"></p-sortIcon>
            </div>
          </th>
          <th class="text-slate-200 font-semibold" pSortableColumn="expiration_date">
            <div class="flex items-center gap-2">
              <span>Expires</span>
              <p-sortIcon field="expiration_date"></p-sortIcon>
            </div>
          </th>
          <th class="text-slate-200 font-semibold text-center">Actions</th>
        </tr>
      </ng-template>

      <!-- Table Body -->
      <ng-template let-proposal let-rowIndex="rowIndex" pTemplate="body">
        <tr class="hover:bg-slate-700/30 transition-colors duration-200">
          <td>
            <p-tableCheckbox [value]="proposal"></p-tableCheckbox>
          </td>
          <td class="text-white font-medium">
            <div class="flex items-center gap-3">
              <div
                class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <i class="pi pi-file text-white text-sm"></i>
              </div>
              <div>
                <div class="font-semibold">{{ proposal.project_title }}</div>
                <div class="text-slate-400 text-sm">ID: {{ proposal.id.substring(0, 8) }}...</div>
              </div>
            </div>
          </td>
          <td class="text-slate-300">
            <div class="flex items-center gap-2">
              <i class="pi pi-user text-slate-400"></i>
              <span>{{ proposal.client?.name || 'Unknown Client' }}</span>
            </div>
          </td>
          <td class="text-white font-semibold">
            {{ formatCurrency(proposal.price, proposal.currency) }}
          </td>
          <td>
            <p-tag
              [severity]="getStatusSeverity(proposal.status)"
              [style]="{ fontSize: '0.75rem', padding: '0.25rem 0.75rem' }"
              [value]="proposal.status">
            </p-tag>
          </td>
          <td class="text-slate-300">
            <div class="flex items-center gap-2">
              <i class="pi pi-calendar text-slate-400 text-sm"></i>
              <span>{{ formatDate(proposal.created_at) }}</span>
            </div>
          </td>
          <td class="text-slate-300">
            <div class="flex items-center gap-2">
              <i class="pi pi-clock text-slate-400 text-sm"></i>
              <span>{{ formatDate(proposal.expiration_date) }}</span>
            </div>
          </td>
          <td>
            <div class="flex items-center justify-center gap-2">
              <button
                (click)="viewProposal(proposal)"
                class="w-8 h-8 bg-blue-500/80 hover:bg-blue-500 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110"
                title="View Proposal">
                <i class="pi pi-eye text-xs"></i>
              </button>
              <button
                (click)="editProposal(proposal)"
                class="w-8 h-8 bg-orange-500/80 hover:bg-orange-500 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110"
                title="Edit Proposal">
                <i class="pi pi-pencil text-xs"></i>
              </button>
              <button
                (click)="sendProposal(proposal)"
                class="w-8 h-8 bg-green-500/80 hover:bg-green-500 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110"
                title="Send Proposal">
                <i class="pi pi-send text-xs"></i>
              </button>
              <button
                (click)="deleteProposal(proposal)"
                class="w-8 h-8 bg-red-500/80 hover:bg-red-500 text-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110"
                title="Delete Proposal">
                <i class="pi pi-trash text-xs"></i>
              </button>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty State -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td class="text-center py-12" colspan="8">
            <div class="flex flex-col items-center gap-4">
              <div class="w-16 h-16 bg-slate-700 rounded-full flex items-center justify-center">
                <i class="pi pi-file text-slate-400 text-2xl"></i>
              </div>
              <div>
                <h3 class="text-slate-300 text-lg font-semibold mb-2">No Proposals Found</h3>
                <p class="text-slate-400 mb-4">Get started by creating your first proposal</p>
                <button
                  (click)="createNewProposal()"
                  class="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold px-6 py-2 rounded-lg transition-all duration-200">
                  Create New Proposal
                </button>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Loading Template -->
      <ng-template pTemplate="loadingbody">
        <tr>
          <td class="text-center py-8" colspan="8">
            <div class="flex items-center justify-center gap-3">
              <i class="pi pi-spin pi-spinner text-purple-400 text-xl"></i>
              <span class="text-slate-300">Loading proposals...</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>

  <!-- Toast Messages -->
  <p-toast position="top-right"></p-toast>

  <!-- Confirmation Dialog -->
  <p-confirmDialog
    [baseZIndex]="10000"
    [style]="{ width: '450px' }"
    rejectButtonStyleClass="p-button-text">
  </p-confirmDialog>
</div>
